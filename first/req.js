var http = require('http'),
    fs = require('fs'),
    ccav = require('./ccavutil.js'),
    crypto = require('crypto'),
    qs = require('querystring');

postReq = function(request,response){
    var body = '',
	  workingKey = '180362C8404F8EA354E49F2E342EF00C',	//Put in the 32-Bit key shared by CCAvenues.
	  accessCode = 'AVPL80MF88CE97LPEC',			//Put in the Access Code shared by CCAvenues.
	  encRequest = '';

    //Generate Md5 hash for the key and then convert in base64 string
    var md5 = crypto.createHash('md5').update(workingKey).digest();
    var keyBase64 = Buffer.from(md5).toString('base64');
    
    //Initializing Vector and then convert in base64 string
    const iv = crypto.randomBytes(16); // Secure random IV
    console.log("iv",iv)
    var ivBase64 = iv.toString('base64');

	  body += request.payment_string;
	  encRequest = ccav.encrypt(body, keyBase64, ivBase64);

    return res.status(200); 
};


// Testing 
const httpMocks = require('./first/node_modules/node-mocks-http/lib/http-mock.js');
const req = httpMocks.createRequest({
  method: 'POST',
  url: '/pay',
  body: {
    payment_string: 'amount=100&currency=INR' // This is your raw form data
  }
});
const res = httpMocks.createResponse();
const response = postReq(req, res);
console.log("response", response);

res.on('end', () => {
  console.log("Response :", res);
  console.log("Response body:", res._getData());
  console.log("Response status:", res.statusCode);
});