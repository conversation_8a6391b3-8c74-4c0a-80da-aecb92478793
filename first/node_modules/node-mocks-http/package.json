{"author": "<PERSON> <<EMAIL>> (http://www.github.com/howardabrams)", "name": "node-mocks-http", "description": "Mock 'http' objects for testing Express, Next.js and Koa routing functions", "version": "1.17.2", "homepage": "https://github.com/eugef/node-mocks-http", "bugs": {"url": "https://github.com/eugef/node-mocks-http/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/howardabrams"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/eugef"}], "license": "MIT", "keywords": ["mock", "stub", "dummy", "nodejs", "js", "testing", "test", "http", "http mock"], "repository": {"type": "git", "url": "git://github.com/eugef/node-mocks-http.git"}, "main": "./lib/http-mock.js", "types": "./lib/http-mock.d.ts", "engines": {"node": ">=14"}, "dependencies": {"accepts": "^1.3.7", "content-disposition": "^0.5.3", "depd": "^1.1.0", "fresh": "^0.5.2", "merge-descriptors": "^1.0.1", "methods": "^1.1.2", "mime": "^1.3.4", "parseurl": "^1.3.3", "range-parser": "^1.2.0", "type-is": "^1.6.18"}, "peerDependencies": {"@types/express": "^4.17.21 || ^5.0.0", "@types/node": "*"}, "peerDependenciesMeta": {"@types/express": {"optional": true}, "@types/node": {"optional": true}}, "devDependencies": {"@types/chai": "^4.3.11", "@types/mocha": "^10.0.6", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "chai": "^4.2.0", "eslint": "^8.56.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "husky": "^8.0.3", "mocha": "^10.2.0", "nyc": "^15.1.0", "prettier": "^3.1.1", "sinon": "^17.0.1", "sinon-chai": "^3.5.0", "ts-node": "^10.9.1", "tsd": "^0.29.0", "typescript": "^5.3.2"}, "scripts": {"test": "mocha", "coverage": "nyc --reporter=html --reporter=lcov --reporter=text-summary mocha", "format": "prettier --write --list-different .", "lint": "eslint --fix .", "check": "npm run check:format && npm run check:lint && npm run check:types", "check:format": "prettier --list-different .", "check:lint": "eslint .", "check:types": "tsd --files ./test/**/*.test-d.ts .", "postversion": "npm publish && git push --follow-tags", "prepare": "husky install"}, "files": ["lib"]}